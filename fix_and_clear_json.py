#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复JSON格式问题并清理generation_history记录
"""

import json
import re
import shutil
from datetime import datetime

def fix_json_format(content):
    """修复常见的JSON格式问题"""
    
    # 修复缺少逗号的问题
    # 修复 "field":"value""field2" 的情况
    content = re.sub(r'",\s*"([^"]+)"([^,\]\}])', r'",\n        "negative_prompt": "\1",', content)
    
    # 修复重复的negative_prompt开头
    content = re.sub(r'"([^"]*)"lowres,', r'"\1", "negative_prompt": "lowres,', content)
    
    return content

def clear_generation_history_robust():
    """强制清理generation_history，即使JSON有格式问题"""
    
    # 备份原文件
    backup_filename = f"users.json.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2("users.json", backup_filename)
    print(f"已创建备份文件: {backup_filename}")
    
    # 读取文件内容
    try:
        with open("users.json", "r", encoding="utf-8") as f:
            content = f.read()
        print("成功读取文件内容")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return False
    
    # 使用正则表达式直接清理generation_history
    print("开始使用正则表达式清理generation_history...")
    
    # 统计清理前的记录数
    history_pattern = r'"generation_history":\s*\[[^\]]*\]'
    matches = re.findall(history_pattern, content, re.DOTALL)
    total_matches = len(matches)
    
    # 计算总记录数（粗略估计）
    total_records = 0
    for match in matches:
        # 计算每个generation_history中的记录数
        record_count = match.count('"type":')
        total_records += record_count
    
    print(f"找到 {total_matches} 个用户的generation_history")
    print(f"估计总记录数: {total_records}")
    
    # 替换所有generation_history为空数组
    new_content = re.sub(history_pattern, '"generation_history": []', content, flags=re.DOTALL)
    
    # 保存修改后的文件
    try:
        with open("users.json", "w", encoding="utf-8") as f:
            f.write(new_content)
        print(f"\n清理完成!")
        print(f"已将所有generation_history清空")
        print(f"文件已保存: users.json")
        
        # 验证JSON格式
        try:
            with open("users.json", "r", encoding="utf-8") as f:
                json.load(f)
            print("JSON格式验证通过!")
            return True
        except Exception as e:
            print(f"JSON格式验证失败: {e}")
            print("但generation_history已经清理完成")
            return True
            
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

if __name__ == "__main__":
    print("开始修复JSON格式并清理generation_history记录...")
    success = clear_generation_history_robust()
    if success:
        print("处理完成!")
    else:
        print("处理失败!")
