#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单有效的 users.json 修复方案
"""

import json
import os
import shutil
from datetime import datetime

def simple_fix_users_json():
    """简单修复方案：使用最新的备份文件"""
    
    users_file = 'users.json'
    
    print("开始简单修复方案...")
    
    # 查找所有备份文件
    backup_files = []
    for filename in os.listdir('.'):
        if filename.startswith('users.json.backup'):
            backup_files.append(filename)
    
    if not backup_files:
        print("未找到备份文件")
        return False
    
    # 按修改时间排序，找到最新的备份
    backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    print(f"找到 {len(backup_files)} 个备份文件:")
    for i, backup in enumerate(backup_files[:5]):  # 只显示前5个
        mtime = datetime.fromtimestamp(os.path.getmtime(backup))
        size = os.path.getsize(backup)
        print(f"  {i+1}. {backup} - {mtime.strftime('%Y-%m-%d %H:%M:%S')} - {size:,} bytes")
    
    # 尝试每个备份文件，直到找到一个可以正常解析的
    for backup_file in backup_files:
        print(f"\n尝试使用备份文件: {backup_file}")
        
        try:
            # 尝试读取和解析备份文件
            with open(backup_file, 'rb') as f:
                raw_data = f.read()
            
            # 尝试不同的编码策略
            for encoding_strategy in ['utf-8', ('utf-8', 'ignore'), ('utf-8', 'replace')]:
                try:
                    if isinstance(encoding_strategy, tuple):
                        text_content = raw_data.decode(encoding_strategy[0], errors=encoding_strategy[1])
                        print(f"  使用编码策略: {encoding_strategy}")
                    else:
                        text_content = raw_data.decode(encoding_strategy)
                        print(f"  使用编码: {encoding_strategy}")
                    
                    # 尝试解析JSON
                    json_data = json.loads(text_content)
                    print(f"  ✓ JSON解析成功！包含 {len(json_data)} 个用户")
                    
                    # 创建当前文件的备份
                    if os.path.exists(users_file):
                        current_backup = f'users.json.broken_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
                        shutil.copy2(users_file, current_backup)
                        print(f"  已备份当前损坏的文件为: {current_backup}")
                    
                    # 恢复备份文件
                    shutil.copy2(backup_file, users_file)
                    print(f"  ✓ 已恢复备份文件到 {users_file}")
                    
                    # 重新格式化并保存（确保格式正确）
                    with open(users_file, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)
                    print("  ✓ 已重新格式化并保存文件")
                    
                    # 最终验证
                    with open(users_file, 'r', encoding='utf-8') as f:
                        test_data = json.load(f)
                    print(f"  ✓ 最终验证成功：{len(test_data)} 个用户记录")
                    
                    return True
                    
                except UnicodeDecodeError as e:
                    print(f"  编码错误: {e}")
                    continue
                except json.JSONDecodeError as e:
                    print(f"  JSON解析错误: {e}")
                    continue
                    
        except Exception as e:
            print(f"  处理备份文件时出错: {e}")
            continue
    
    print("\n所有备份文件都无法使用")
    return False

def create_emergency_users_file():
    """创建紧急用户文件"""
    print("\n创建紧急用户文件...")
    
    # 基本的管理员用户
    emergency_users = {
        "menyu": {
            "username": "menyu",
            "password": "8e31e19f4ca4b8302a4446031fa55f06058a5f1535929f09f071843d865b28cfd2e4853ef391de2ed3f6b5a429f9499d",
            "email": "<EMAIL>",
            "points": 1000,
            "created_at": datetime.now().isoformat(),
            "last_login": datetime.now().isoformat(),
            "is_admin": True,
            "total_generated": 0,
            "generation_history": [],
            "status": "approved",
            "approved_at": datetime.now().isoformat(),
            "approved_by": "system"
        }
    }
    
    emergency_file = f'users.json.emergency_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    
    try:
        with open(emergency_file, 'w', encoding='utf-8') as f:
            json.dump(emergency_users, f, ensure_ascii=False, indent=2)
        
        print(f"紧急用户文件已创建: {emergency_file}")
        print("可以将此文件重命名为 users.json 来恢复基本功能")
        print("注意：这将丢失所有现有用户数据")
        
        return emergency_file
        
    except Exception as e:
        print(f"创建紧急用户文件失败: {e}")
        return None

if __name__ == "__main__":
    success = simple_fix_users_json()
    
    if success:
        print("\n✓ 修复成功！系统应该可以正常运行了。")
    else:
        print("\n✗ 修复失败！")
        emergency_file = create_emergency_users_file()
        
        if emergency_file:
            print(f"\n建议操作:")
            print(f"1. 备份当前的 users.json 文件")
            print(f"2. 将 {emergency_file} 重命名为 users.json")
            print(f"3. 重启应用程序")
            print(f"4. 使用管理员账户 menyu 登录系统")
            print(f"5. 从其他备份中手动恢复用户数据（如果需要）")
