#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理users.json文件中所有用户的generation_history记录
"""

import json
import shutil
from datetime import datetime

def clear_generation_history():
    """清理所有用户的generation_history记录"""
    
    # 备份原文件
    backup_filename = f"users.json.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2("users.json", backup_filename)
    print(f"已创建备份文件: {backup_filename}")
    
    # 读取JSON文件，尝试不同的编码
    data = None
    for encoding in ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1']:
        try:
            with open("users.json", "r", encoding=encoding) as f:
                data = json.load(f)
            print(f"成功读取users.json文件 (编码: {encoding})")
            break
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件失败 (编码: {encoding}): {e}")
            continue

    if data is None:
        print("无法读取文件，尝试了所有编码方式")
        return False
    
    # 统计信息
    total_users = len(data)
    users_with_history = 0
    total_history_records = 0
    
    # 清理每个用户的generation_history
    for username, user_data in data.items():
        if "generation_history" in user_data:
            history_count = len(user_data["generation_history"])
            if history_count > 0:
                users_with_history += 1
                total_history_records += history_count
                print(f"用户 {username}: 清理了 {history_count} 条记录")
            
            # 清空generation_history
            user_data["generation_history"] = []
    
    # 保存清理后的文件
    try:
        with open("users.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"\n清理完成!")
        print(f"总用户数: {total_users}")
        print(f"有历史记录的用户数: {users_with_history}")
        print(f"总共清理的记录数: {total_history_records}")
        print(f"文件已保存: users.json")
        return True
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

if __name__ == "__main__":
    print("开始清理users.json中的generation_history记录...")
    success = clear_generation_history()
    if success:
        print("清理成功完成!")
    else:
        print("清理失败!")
