#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from auth import UserManager
import json

user_manager = UserManager()
print('测试改进后的保存功能...')
result = user_manager.save_users()
print(f'保存结果: {"成功" if result else "失败"}')

# 验证文件是否正确保存
try:
    with open('users.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    print(f'验证成功: 文件包含 {len(data)} 个用户')
except Exception as e:
    print(f'验证失败: {e}')
