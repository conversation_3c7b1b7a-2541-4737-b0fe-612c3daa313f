#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
迭代修复 users.json 文件的所有类似问题
"""

import json
import os
import shutil
from datetime import datetime
import re

def fix_concatenated_strings(text_content):
    """修复被连接的字符串"""
    
    # 常见的连接模式
    patterns = [
        # model_name 后面直接跟 https://
        (r'"model_name":\s*"([^"]*)"(https://[^"]*)"', 'model_name', 'image_url'),
        # negative_prompt 后面直接跟其他内容
        (r'"negative_prompt":\s*"([^"]*)"([^",\n]*)"', 'negative_prompt', None),
        # 其他可能的模式
        (r'"([^"]+)":\s*"([^"]*)"([^",\n]+)"', None, None),
    ]
    
    lines = text_content.split('\n')
    modified = False
    
    for i, line in enumerate(lines):
        original_line = line
        
        for pattern, field1, field2 in patterns:
            match = re.search(pattern, line)
            if match:
                print(f"在第 {i+1} 行发现连接字符串问题:")
                print(f"  原始行: {repr(line)}")
                
                if field1 == 'model_name' and field2 == 'image_url':
                    # 特殊处理 model_name + image_url
                    model_name = match.group(1)
                    image_url = match.group(2)
                    
                    # 修复被截断的模型名称
                    if model_name.endswith(" Do"):
                        model_name = "Miaomiao Harem vPred Dogma 1.1"
                    
                    # 获取缩进
                    indent_match = re.match(r'^(\s*)', line)
                    indent = indent_match.group(1) if indent_match else '        '
                    
                    # 创建修复后的行
                    fixed_line = f'{indent}"model_name": "{model_name}",'
                    image_line = f'{indent}"image_url": "{image_url}",'
                    
                    lines[i] = fixed_line
                    lines.insert(i + 1, image_line)
                    
                    print(f"  修复为:")
                    print(f"    {repr(fixed_line)}")
                    print(f"    {repr(image_line)}")
                    
                    modified = True
                    break
                    
                elif field1 == 'negative_prompt':
                    # 特殊处理 negative_prompt
                    prompt_part1 = match.group(1)
                    prompt_part2 = match.group(2)
                    
                    # 合并两部分，去掉多余的引号
                    full_prompt = prompt_part1 + prompt_part2.strip('"')
                    
                    # 获取缩进
                    indent_match = re.match(r'^(\s*)', line)
                    indent = indent_match.group(1) if indent_match else '        '
                    
                    # 创建修复后的行
                    fixed_line = f'{indent}"negative_prompt": "{full_prompt}",'
                    
                    lines[i] = fixed_line
                    
                    print(f"  修复为: {repr(fixed_line)}")
                    
                    modified = True
                    break
                    
                else:
                    # 通用处理：尝试分割字符串
                    if len(match.groups()) >= 3:
                        field_name = match.group(1)
                        value1 = match.group(2)
                        value2 = match.group(3)
                        
                        # 获取缩进
                        indent_match = re.match(r'^(\s*)', line)
                        indent = indent_match.group(1) if indent_match else '        '
                        
                        # 尝试智能合并
                        if value2.startswith('"') and value2.endswith('"'):
                            # value2 看起来是另一个完整的字符串值，可能是另一个字段
                            fixed_line = f'{indent}"{field_name}": "{value1}",'
                            lines[i] = fixed_line
                            print(f"  修复为: {repr(fixed_line)}")
                            modified = True
                            break
                        else:
                            # 合并值
                            full_value = value1 + value2.strip('"')
                            fixed_line = f'{indent}"{field_name}": "{full_value}",'
                            lines[i] = fixed_line
                            print(f"  修复为: {repr(fixed_line)}")
                            modified = True
                            break
    
    if modified:
        return '\n'.join(lines)
    else:
        return text_content

def iterative_fix_json():
    """迭代修复JSON文件"""
    
    users_file = 'users.json'
    backup_file = f'users.json.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    
    print(f"开始迭代修复 {users_file} 文件...")
    
    # 创建备份
    if os.path.exists(users_file):
        shutil.copy2(users_file, backup_file)
        print(f"已创建备份文件: {backup_file}")
    else:
        print(f"错误: {users_file} 文件不存在")
        return False
    
    max_iterations = 10
    iteration = 0
    
    try:
        while iteration < max_iterations:
            iteration += 1
            print(f"\n=== 第 {iteration} 次迭代 ===")
            
            # 读取文件
            with open(users_file, 'rb') as f:
                raw_data = f.read()
            
            # 处理编码问题
            text_content = raw_data.decode('utf-8', errors='replace')
            
            # 尝试解析JSON
            try:
                json_data = json.loads(text_content)
                print("JSON解析成功！修复完成。")
                
                # 重新保存以确保格式正确
                with open(users_file, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, ensure_ascii=False, indent=2)
                
                print(f"验证：文件包含 {len(json_data)} 个用户记录")
                return True
                
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                
                error_line = getattr(e, 'lineno', None)
                error_col = getattr(e, 'colno', None)
                
                if error_line and error_col:
                    print(f"错误位置: 第 {error_line} 行，第 {error_col} 列")
                    
                    # 修复连接的字符串
                    fixed_content = fix_concatenated_strings(text_content)
                    
                    if fixed_content != text_content:
                        # 保存修复后的内容
                        with open(users_file, 'w', encoding='utf-8') as f:
                            f.write(fixed_content)
                        print("已应用修复，继续下一次迭代...")
                        continue
                    else:
                        print("未找到可修复的模式")
                        
                        # 显示错误行内容
                        lines = text_content.split('\n')
                        if error_line <= len(lines):
                            problem_line = lines[error_line - 1]
                            print(f"问题行内容: {repr(problem_line)}")
                            
                            # 尝试简单的修复：移除多余的引号或添加缺失的逗号
                            if 'delimiter' in str(e).lower():
                                # 可能缺少逗号
                                if error_col > 1:
                                    char_before = problem_line[error_col - 2] if error_col - 2 >= 0 else ''
                                    char_at = problem_line[error_col - 1] if error_col - 1 >= 0 else ''
                                    
                                    if char_before in ['}', ']', '"'] and char_at in ['"', '{', '[']:
                                        print("尝试添加逗号...")
                                        fixed_line = problem_line[:error_col-1] + ',' + problem_line[error_col-1:]
                                        lines[error_line - 1] = fixed_line
                                        
                                        fixed_content = '\n'.join(lines)
                                        with open(users_file, 'w', encoding='utf-8') as f:
                                            f.write(fixed_content)
                                        print("已添加逗号，继续下一次迭代...")
                                        continue
                        
                        print("无法自动修复此错误")
                        return False
                else:
                    print("无法获取错误位置信息")
                    return False
        
        print(f"达到最大迭代次数 ({max_iterations})，修复失败")
        return False
        
    except Exception as e:
        print(f"修复过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = iterative_fix_json()
    if success:
        print("\n修复成功！")
    else:
        print("\n修复失败！")
