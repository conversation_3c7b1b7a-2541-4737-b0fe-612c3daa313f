#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 users.json 文件中的 UTF-8 编码问题
"""

import json
import os
import shutil
from datetime import datetime

def find_all_utf8_errors(data):
    """找到所有UTF-8编码错误的位置"""
    errors = []
    i = 0
    while i < len(data):
        try:
            # 尝试解码单个字符
            if data[i] < 0x80:
                # ASCII字符
                i += 1
            elif data[i] < 0xC0:
                # 无效的UTF-8开始字节
                errors.append((i, f"无效的UTF-8开始字节: 0x{data[i]:02x}"))
                i += 1
            elif data[i] < 0xE0:
                # 2字节UTF-8字符
                if i + 1 >= len(data) or (data[i+1] & 0xC0) != 0x80:
                    errors.append((i, f"截断的2字节UTF-8字符: 0x{data[i]:02x}"))
                    i += 1
                else:
                    i += 2
            elif data[i] < 0xF0:
                # 3字节UTF-8字符
                if i + 2 >= len(data) or (data[i+1] & 0xC0) != 0x80 or (data[i+2] & 0xC0) != 0x80:
                    errors.append((i, f"截断的3字节UTF-8字符: 0x{data[i]:02x}"))
                    i += 1
                else:
                    i += 3
            elif data[i] < 0xF8:
                # 4字节UTF-8字符
                if i + 3 >= len(data) or (data[i+1] & 0xC0) != 0x80 or (data[i+2] & 0xC0) != 0x80 or (data[i+3] & 0xC0) != 0x80:
                    errors.append((i, f"截断的4字节UTF-8字符: 0x{data[i]:02x}"))
                    i += 1
                else:
                    i += 4
            else:
                # 无效的UTF-8字节
                errors.append((i, f"无效的UTF-8字节: 0x{data[i]:02x}"))
                i += 1
        except IndexError:
            errors.append((i, "文件意外结束"))
            break

    return errors

def fix_utf8_string(string_bytes):
    """修复UTF-8字符串，移除截断的字符"""
    fixed_bytes = bytearray()
    i = 0
    while i < len(string_bytes):
        byte = string_bytes[i]

        if byte < 0x80:
            # ASCII字符
            fixed_bytes.append(byte)
            i += 1
        elif byte < 0xC0:
            # 无效的UTF-8开始字节，跳过
            print(f"跳过无效字节 0x{byte:02x} at position {i}")
            i += 1
        elif byte < 0xE0:
            # 2字节UTF-8字符
            if i + 1 < len(string_bytes) and (string_bytes[i+1] & 0xC0) == 0x80:
                fixed_bytes.extend(string_bytes[i:i+2])
                i += 2
            else:
                print(f"截断的2字节UTF-8字符 at position {i}")
                break
        elif byte < 0xF0:
            # 3字节UTF-8字符
            if i + 2 < len(string_bytes) and (string_bytes[i+1] & 0xC0) == 0x80 and (string_bytes[i+2] & 0xC0) == 0x80:
                fixed_bytes.extend(string_bytes[i:i+3])
                i += 3
            else:
                print(f"截断的3字节UTF-8字符 at position {i}")
                break
        elif byte < 0xF8:
            # 4字节UTF-8字符
            if i + 3 < len(string_bytes) and (string_bytes[i+1] & 0xC0) == 0x80 and (string_bytes[i+2] & 0xC0) == 0x80 and (string_bytes[i+3] & 0xC0) == 0x80:
                fixed_bytes.extend(string_bytes[i:i+4])
                i += 4
            else:
                print(f"截断的4字节UTF-8字符 at position {i}")
                break
        else:
            # 无效的UTF-8字节
            print(f"无效的UTF-8字节 0x{byte:02x} at position {i}")
            i += 1

    return fixed_bytes

def fix_users_json():
    """修复 users.json 文件中的编码问题"""

    # 文件路径
    users_file = 'users.json'
    backup_file = f'users.json.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'

    print(f"开始修复 {users_file} 文件...")

    # 创建备份
    if os.path.exists(users_file):
        shutil.copy2(users_file, backup_file)
        print(f"已创建备份文件: {backup_file}")
    else:
        print(f"错误: {users_file} 文件不存在")
        return False

    try:
        # 尝试以二进制模式读取文件
        with open(users_file, 'rb') as f:
            data = f.read()

        print(f"文件大小: {len(data)} 字节")

        # 找到所有UTF-8编码错误
        errors = find_all_utf8_errors(data)
        print(f"发现 {len(errors)} 个UTF-8编码错误")

        if not errors:
            print("没有发现UTF-8编码错误")
            return True

        # 显示前10个错误
        for i, (pos, msg) in enumerate(errors[:10]):
            print(f"错误 {i+1}: 位置 {pos} - {msg}")

        if len(errors) > 10:
            print(f"... 还有 {len(errors) - 10} 个错误")

        # 尝试使用错误处理策略重新解码
        try:
            # 使用 'ignore' 策略忽略无效字符
            text_content = data.decode('utf-8', errors='ignore')
            print("使用 'ignore' 策略成功解码文件")

            # 尝试解析为JSON
            try:
                json_data = json.loads(text_content)
                print("成功解析为JSON数据")

                # 重新保存文件
                with open(users_file, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, ensure_ascii=False, indent=2)

                print("文件修复完成！")
                return True

            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")

                # 尝试使用 'replace' 策略
                text_content = data.decode('utf-8', errors='replace')
                print("使用 'replace' 策略重新解码")

                try:
                    json_data = json.loads(text_content)
                    print("成功解析为JSON数据")

                    # 重新保存文件
                    with open(users_file, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)

                    print("文件修复完成！")
                    return True

                except json.JSONDecodeError as e2:
                    print(f"使用 'replace' 策略仍然JSON解析失败: {e2}")
                    return False

        except UnicodeDecodeError as e:
            print(f"即使使用错误处理策略仍然解码失败: {e}")
            return False

    except Exception as e:
        print(f"修复过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = fix_users_json()
    if success:
        print("修复成功！")
    else:
        print("修复失败！")
