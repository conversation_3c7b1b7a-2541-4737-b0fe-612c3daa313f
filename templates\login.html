<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - MiaoMiao AI绘图</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            padding: 2rem;
            width: 100%;
            max-width: 450px;
            margin: 20px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #333;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #666;
            margin-bottom: 0;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-floating input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .form-floating input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-register {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-register:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 1rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .back-to-home {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .back-to-home:hover {
            color: #f8f9fa;
            transform: translateX(-5px);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1rem;
        }
        
        .tab-buttons {
            display: flex;
            margin-bottom: 2rem;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
        }
        
        .tab-button {
            flex: 1;
            background: transparent;
            border: none;
            padding: 10px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: #666;
        }
        
        .tab-button.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .form-section {
            display: none;
        }
        
        .form-section.active {
            display: block;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }

        .verification-code-group {
            display: flex;
            gap: 10px;
            margin-bottom: 1rem;
        }

        .verification-code-group .form-floating {
            flex: 1;
        }

        .verification-code-group button {
            min-width: 120px;
            height: 58px;
            border-radius: 10px;
            font-weight: 600;
            white-space: nowrap;
        }

        .verification-code-group button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .oauth-login {
            margin-top: 1.5rem;
        }

        .btn-linux-do {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            width: 100%;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-linux-do:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-linux-do i {
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <a href="/" class="back-to-home">
        <i class="fas fa-arrow-left"></i> 返回首页
    </a>
    
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-palette"></i> MiaoMiao AI</h1>
            <p>欢迎使用AI绘图平台</p>
        </div>
        
        <div class="tab-buttons">
            <button class="tab-button active" data-tab="login">登录</button>
            <button class="tab-button" data-tab="register">注册</button>
        </div>
        
        <!-- 登录表单 -->
        <div id="loginSection" class="form-section active">
            <form id="loginForm">
                <div class="form-floating">
                    <input type="text" class="form-control" id="loginUsername" placeholder="用户名" required>
                    <label for="loginUsername"><i class="fas fa-user"></i> 用户名</label>
                </div>
                
                <div class="form-floating">
                    <input type="password" class="form-control" id="loginPassword" placeholder="密码" required>
                    <label for="loginPassword"><i class="fas fa-lock"></i> 密码</label>
                </div>
                
                <div id="loginMessage" class="alert" style="display: none;"></div>
                
                <button type="submit" class="btn btn-primary btn-login">
                    <span class="loading spinner-border spinner-border-sm me-2" role="status"></span>
                    登录
                </button>
            </form>

            <div class="divider">
                <span>或</span>
            </div>

            <div class="oauth-login">
                <a href="/auth/linux_do" class="btn btn-linux-do">
                    <i class="fab fa-linux"></i>
                    Linux Do 快捷登录
                </a>
                <div class="text-center mt-2">
                    <small class="text-muted">使用Linux Do账号登录，未注册自动注册</small>
                </div>
            </div>
        </div>
        
        <!-- 注册表单 -->
        <div id="registerSection" class="form-section">
            <form id="registerForm">
                <div class="form-floating">
                    <input type="text" class="form-control" id="registerUsername" placeholder="用户名" required>
                    <label for="registerUsername"><i class="fas fa-user"></i> 用户名</label>
                    <div class="form-text">至少3个字符</div>
                </div>
                
                <div class="form-floating">
                    <input type="password" class="form-control" id="registerPassword" placeholder="密码" required>
                    <label for="registerPassword"><i class="fas fa-lock"></i> 密码</label>
                    <div class="form-text">至少6个字符</div>
                </div>
                
                <div class="form-floating">
                    <input type="email" class="form-control" id="registerEmail" placeholder="邮箱" required>
                    <label for="registerEmail"><i class="fas fa-envelope"></i> QQ邮箱 *</label>
                    <div class="form-text">仅限使用QQ邮箱（@qq.com）注册</div>
                </div>

                <div class="verification-code-group">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="verificationCode" placeholder="验证码" maxlength="6" required>
                        <label for="verificationCode"><i class="fas fa-shield-alt"></i> 邮箱验证码 *</label>
                    </div>
                    <button type="button" class="btn btn-outline-primary" id="sendCodeBtn">
                        <span class="send-code-loading spinner-border spinner-border-sm me-2" role="status" style="display: none;"></span>
                        发送验证码
                    </button>
                </div>
                <div class="form-text mb-3">请先输入邮箱，然后点击发送验证码</div>

                <div class="form-floating">
                    <textarea class="form-control" id="registerReason" placeholder="申请理由" style="height: 100px" required></textarea>
                    <label for="registerReason"><i class="fas fa-comment"></i> 申请理由 *</label>
                    <div class="form-text">请简要说明您的使用目的（至少10个字符）</div>
                </div>
                
                <div id="registerMessage" class="alert" style="display: none;"></div>
                
                <button type="submit" class="btn btn-primary btn-register">
                    <span class="loading spinner-border spinner-border-sm me-2" role="status"></span>
                    注册
                </button>
                
                <div class="text-center mt-3">
                    <small class="text-muted">注册即送10积分，每次生成消耗1积分</small>
                </div>
            </form>

            <div class="divider">
                <span>或</span>
            </div>

            <div class="oauth-login">
                <a href="/auth/linux_do" class="btn btn-linux-do">
                    <i class="fab fa-linux"></i>
                    Linux Do 快捷注册
                </a>
                <div class="text-center mt-2">
                    <small class="text-muted">使用Linux Do账号快速注册并登录</small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 检查URL参数中是否有OAuth错误信息
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const error = urlParams.get('error');
            const message = urlParams.get('message');

            if (error === 'oauth_error' && message) {
                const loginMessageDiv = document.getElementById('loginMessage');
                showMessage(loginMessageDiv, decodeURIComponent(message), 'danger');

                // 清除URL参数
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            }
        });
    </script>
    <script>
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tab = this.dataset.tab;
                
                // 更新按钮状态
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // 更新表单显示
                document.querySelectorAll('.form-section').forEach(section => section.classList.remove('active'));
                document.getElementById(tab + 'Section').classList.add('active');
                
                // 清除消息
                document.querySelectorAll('.alert').forEach(alert => alert.style.display = 'none');
            });
        });
        
        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value.trim();
            const messageDiv = document.getElementById('loginMessage');
            const submitBtn = this.querySelector('button[type="submit"]');
            const loading = submitBtn.querySelector('.loading');
            
            if (!username || !password) {
                showMessage(messageDiv, '请填写用户名和密码', 'danger');
                return;
            }
            
            // 显示加载状态
            submitBtn.disabled = true;
            loading.classList.add('show');
            
            fetch('/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(messageDiv, data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    showMessage(messageDiv, data.message, 'danger');
                }
            })
            .catch(error => {
                showMessage(messageDiv, `登录时发生错误: ${error}`, 'danger');
            })
            .finally(() => {
                submitBtn.disabled = false;
                loading.classList.remove('show');
            });
        });
        
        // 发送验证码功能
        let countdownTimer = null;
        document.getElementById('sendCodeBtn').addEventListener('click', function() {
            const email = document.getElementById('registerEmail').value.trim();
            const messageDiv = document.getElementById('registerMessage');
            const sendBtn = this;
            const loading = sendBtn.querySelector('.send-code-loading');

            if (!email) {
                showMessage(messageDiv, '请先输入邮箱地址', 'danger');
                return;
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage(messageDiv, '请输入有效的邮箱地址', 'danger');
                return;
            }

            // 验证是否为QQ邮箱
            if (!email.toLowerCase().endsWith('@qq.com')) {
                showMessage(messageDiv, '仅支持QQ邮箱（@qq.com）', 'danger');
                return;
            }

            // 显示加载状态
            sendBtn.disabled = true;
            loading.style.display = 'inline-block';

            fetch('/send_verification_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(messageDiv, data.message, 'success');

                    // 开始倒计时
                    let countdown = 60;
                    sendBtn.textContent = `${countdown}秒后重发`;

                    countdownTimer = setInterval(() => {
                        countdown--;
                        if (countdown > 0) {
                            sendBtn.textContent = `${countdown}秒后重发`;
                        } else {
                            clearInterval(countdownTimer);
                            sendBtn.textContent = '发送验证码';
                            sendBtn.disabled = false;
                        }
                    }, 1000);
                } else {
                    showMessage(messageDiv, data.message, 'danger');
                    sendBtn.disabled = false;
                }
            })
            .catch(error => {
                showMessage(messageDiv, `发送验证码失败: ${error}`, 'danger');
                sendBtn.disabled = false;
            })
            .finally(() => {
                loading.style.display = 'none';
            });
        });

        // 注册表单提交
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('registerUsername').value.trim();
            const password = document.getElementById('registerPassword').value.trim();
            const email = document.getElementById('registerEmail').value.trim();
            const reason = document.getElementById('registerReason').value.trim();
            const verificationCode = document.getElementById('verificationCode').value.trim();
            const messageDiv = document.getElementById('registerMessage');
            const submitBtn = this.querySelector('button[type="submit"]');
            const loading = submitBtn.querySelector('.loading');

            if (!username || !password || !email || !reason || !verificationCode) {
                showMessage(messageDiv, '请填写所有必填项', 'danger');
                return;
            }

            if (username.length < 3) {
                showMessage(messageDiv, '用户名至少需要3个字符', 'danger');
                return;
            }

            if (password.length < 6) {
                showMessage(messageDiv, '密码至少需要6个字符', 'danger');
                return;
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage(messageDiv, '请输入有效的邮箱地址', 'danger');
                return;
            }

            // 验证是否为QQ邮箱
            if (!email.toLowerCase().endsWith('@qq.com')) {
                showMessage(messageDiv, '注册仅限使用QQ邮箱（@qq.com）', 'danger');
                return;
            }

            if (reason.length < 10) {
                showMessage(messageDiv, '申请理由至少需要10个字符', 'danger');
                return;
            }

            if (verificationCode.length !== 6) {
                showMessage(messageDiv, '请输入6位验证码', 'danger');
                return;
            }

            // 显示加载状态
            submitBtn.disabled = true;
            loading.classList.add('show');

            fetch('/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    email: email,
                    reason: reason,
                    verification_code: verificationCode
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(messageDiv, data.message, 'success');

                    if (data.require_approval) {
                        // 需要审核的情况
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 2000);
                    } else {
                        // 不需要审核，直接跳转
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1000);
                    }
                } else {
                    showMessage(messageDiv, data.message, 'danger');
                }
            })
            .catch(error => {
                showMessage(messageDiv, `注册时发生错误: ${error}`, 'danger');
            })
            .finally(() => {
                submitBtn.disabled = false;
                loading.classList.remove('show');
            });
        });
        
        // 显示消息的辅助函数
        function showMessage(element, message, type) {
            element.className = `alert alert-${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
