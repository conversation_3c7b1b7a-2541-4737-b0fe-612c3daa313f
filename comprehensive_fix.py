#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面修复 users.json 文件的编码和格式问题
"""

import json
import os
import shutil
from datetime import datetime
import re

def fix_users_json_comprehensive():
    """全面修复 users.json 文件"""
    
    # 文件路径
    users_file = 'users.json'
    backup_file = f'users.json.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    
    print(f"开始全面修复 {users_file} 文件...")
    
    # 创建备份
    if os.path.exists(users_file):
        shutil.copy2(users_file, backup_file)
        print(f"已创建备份文件: {backup_file}")
    else:
        print(f"错误: {users_file} 文件不存在")
        return False
    
    try:
        # 第一步：修复编码问题
        print("第一步：修复UTF-8编码问题...")
        with open(users_file, 'rb') as f:
            raw_data = f.read()
        
        # 使用 'replace' 策略处理编码错误
        text_content = raw_data.decode('utf-8', errors='replace')
        print(f"解码后文本长度: {len(text_content)} 字符")
        
        # 第二步：尝试修复JSON格式问题
        print("第二步：尝试修复JSON格式问题...")
        
        # 先尝试直接解析
        try:
            json_data = json.loads(text_content)
            print("JSON解析成功，无需格式修复")
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print("尝试修复JSON格式...")
            
            # 常见的JSON格式问题修复
            fixed_content = text_content
            
            # 1. 修复多余的逗号
            fixed_content = re.sub(r',(\s*[}\]])', r'\1', fixed_content)
            
            # 2. 修复缺少的逗号（在对象或数组元素之间）
            # 这个比较复杂，先跳过
            
            # 3. 修复未闭合的字符串（替换字符可能导致的问题）
            # 查找并修复可能的问题
            
            try:
                json_data = json.loads(fixed_content)
                print("JSON格式修复成功")
                text_content = fixed_content
            except json.JSONDecodeError as e2:
                print(f"JSON格式修复失败: {e2}")
                
                # 尝试更激进的修复方法：逐行检查
                print("尝试逐行修复...")
                lines = text_content.split('\n')
                
                # 找到问题行
                error_line = getattr(e2, 'lineno', None)
                error_col = getattr(e2, 'colno', None)
                
                if error_line and error_col:
                    print(f"错误位置: 第 {error_line} 行，第 {error_col} 列")
                    if error_line <= len(lines):
                        problem_line = lines[error_line - 1]
                        print(f"问题行内容: {repr(problem_line)}")
                        
                        # 尝试修复这一行
                        # 如果是逗号问题
                        if 'delimiter' in str(e2):
                            # 检查是否缺少逗号
                            if error_col > 1:
                                char_before = problem_line[error_col - 2] if error_col - 2 >= 0 else ''
                                char_at = problem_line[error_col - 1] if error_col - 1 >= 0 else ''
                                
                                print(f"错误位置前的字符: {repr(char_before)}")
                                print(f"错误位置的字符: {repr(char_at)}")
                                
                                # 如果前面是 } 或 ] 或 " 而当前是 " 或 {，可能缺少逗号
                                if char_before in ['}', ']', '"'] and char_at in ['"', '{']:
                                    print("尝试添加逗号...")
                                    fixed_line = problem_line[:error_col-1] + ',' + problem_line[error_col-1:]
                                    lines[error_line - 1] = fixed_line
                                    
                                    fixed_content = '\n'.join(lines)
                                    try:
                                        json_data = json.loads(fixed_content)
                                        print("通过添加逗号修复成功")
                                        text_content = fixed_content
                                    except json.JSONDecodeError as e3:
                                        print(f"添加逗号后仍有错误: {e3}")
                                        return False
                                else:
                                    print("无法自动修复此JSON错误")
                                    return False
                            else:
                                print("无法确定修复方法")
                                return False
                        else:
                            print("未知的JSON错误类型")
                            return False
                    else:
                        print("错误行号超出文件范围")
                        return False
                else:
                    print("无法获取错误位置信息")
                    return False
        
        # 第三步：重新保存文件
        print("第三步：保存修复后的文件...")
        with open(users_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        print("文件修复完成！")
        
        # 第四步：验证修复结果
        print("第四步：验证修复结果...")
        try:
            with open(users_file, 'r', encoding='utf-8') as f:
                test_data = json.load(f)
            print(f"验证成功：文件包含 {len(test_data)} 个用户记录")
            return True
        except Exception as e:
            print(f"验证失败: {e}")
            # 恢复备份
            shutil.copy2(backup_file, users_file)
            print("已恢复备份文件")
            return False
            
    except Exception as e:
        print(f"修复过程中出现错误: {e}")
        return False

def create_minimal_users_json():
    """创建一个最小的用户文件作为备用方案"""
    print("创建最小用户文件作为备用方案...")
    
    minimal_users = {
        "menyu": {
            "username": "menyu",
            "password": "8e31e19f4ca4b8302a4446031fa55f06058a5f1535929f09f071843d865b28cfd2e4853ef391de2ed3f6b5a429f9499d",
            "email": "<EMAIL>",
            "points": 1000,
            "created_at": datetime.now().isoformat(),
            "last_login": datetime.now().isoformat(),
            "is_admin": True,
            "total_generated": 0,
            "generation_history": [],
            "status": "approved",
            "approved_at": datetime.now().isoformat(),
            "approved_by": "system"
        }
    }
    
    backup_file = f'users.json.minimal_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    
    try:
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(minimal_users, f, ensure_ascii=False, indent=2)
        print(f"最小用户文件已创建: {backup_file}")
        return backup_file
    except Exception as e:
        print(f"创建最小用户文件失败: {e}")
        return None

if __name__ == "__main__":
    success = fix_users_json_comprehensive()
    if success:
        print("修复成功！")
    else:
        print("修复失败！")
        print("是否要创建最小用户文件作为备用方案？(y/n)")
        # 在脚本中直接创建，不等待用户输入
        minimal_file = create_minimal_users_json()
        if minimal_file:
            print(f"建议：可以将 {minimal_file} 重命名为 users.json 来恢复基本功能")
            print("注意：这将丢失所有现有用户数据，请谨慎操作")
