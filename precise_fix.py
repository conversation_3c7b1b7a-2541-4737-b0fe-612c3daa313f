#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确修复 users.json 文件的特定问题
"""

import json
import os
import shutil
from datetime import datetime
import re

def fix_specific_json_error():
    """修复特定的JSON错误"""
    
    # 文件路径
    users_file = 'users.json'
    backup_file = f'users.json.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    
    print(f"开始精确修复 {users_file} 文件...")
    
    # 创建备份
    if os.path.exists(users_file):
        shutil.copy2(users_file, backup_file)
        print(f"已创建备份文件: {backup_file}")
    else:
        print(f"错误: {users_file} 文件不存在")
        return False
    
    try:
        # 读取文件内容
        with open(users_file, 'rb') as f:
            raw_data = f.read()
        
        # 使用 'replace' 策略处理编码错误
        text_content = raw_data.decode('utf-8', errors='replace')
        
        # 找到问题行
        lines = text_content.split('\n')
        problem_line_num = 46601 - 1  # 转换为0基索引
        
        if problem_line_num < len(lines):
            problem_line = lines[problem_line_num]
            print(f"问题行 {problem_line_num + 1}: {repr(problem_line)}")
            
            # 分析问题：看起来是两个字符串被连接了
            # "model_name": "Miaomiao Harem vPred Do"https://i0.wp.com/...
            # 应该是：
            # "model_name": "Miaomiao Harem vPred Dogma 1.1",
            # "image_url": "https://i0.wp.com/...
            
            # 使用正则表达式修复这个问题
            pattern = r'"model_name":\s*"([^"]*)"(https://[^"]*)"'
            match = re.search(pattern, problem_line)
            
            if match:
                model_name = match.group(1)
                image_url = match.group(2)
                
                print(f"检测到的模型名称: {repr(model_name)}")
                print(f"检测到的图片URL: {repr(image_url)}")
                
                # 修复模型名称（如果被截断）
                if model_name.endswith(" Do"):
                    model_name = "Miaomiao Harem vPred Dogma 1.1"
                    print(f"修复后的模型名称: {repr(model_name)}")
                
                # 重构这一行
                # 找到这一行的缩进
                indent_match = re.match(r'^(\s*)', problem_line)
                indent = indent_match.group(1) if indent_match else '        '
                
                # 创建修复后的行
                fixed_line = f'{indent}"model_name": "{model_name}",'
                
                # 还需要添加image_url行
                image_line = f'{indent}"image_url": "{image_url}",'
                
                print(f"修复后的model_name行: {repr(fixed_line)}")
                print(f"新的image_url行: {repr(image_line)}")
                
                # 替换问题行
                lines[problem_line_num] = fixed_line
                
                # 在下一行插入image_url（如果下一行不是image_url）
                next_line_num = problem_line_num + 1
                if next_line_num < len(lines):
                    next_line = lines[next_line_num].strip()
                    if not next_line.startswith('"image_url"'):
                        lines.insert(next_line_num, image_line)
                        print("已插入image_url行")
                    else:
                        # 如果下一行已经是image_url，替换它
                        lines[next_line_num] = image_line
                        print("已替换现有的image_url行")
                else:
                    # 如果是最后一行，直接添加
                    lines.append(image_line)
                    print("已在文件末尾添加image_url行")
                
                # 重新组合文件内容
                fixed_content = '\n'.join(lines)
                
                # 尝试解析JSON
                try:
                    json_data = json.loads(fixed_content)
                    print("JSON解析成功！")
                    
                    # 保存修复后的文件
                    with open(users_file, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)
                    
                    print("文件修复并保存成功！")
                    
                    # 验证
                    with open(users_file, 'r', encoding='utf-8') as f:
                        test_data = json.load(f)
                    print(f"验证成功：文件包含 {len(test_data)} 个用户记录")
                    return True
                    
                except json.JSONDecodeError as e:
                    print(f"修复后仍有JSON错误: {e}")
                    
                    # 如果还有错误，尝试找到下一个错误
                    error_line = getattr(e, 'lineno', None)
                    if error_line:
                        print(f"下一个错误在第 {error_line} 行")
                        if error_line <= len(lines):
                            print(f"错误行内容: {repr(lines[error_line - 1])}")
                    
                    return False
                    
            else:
                print("未找到预期的模式，尝试其他修复方法...")
                
                # 尝试简单的字符串分割修复
                if '"https://' in problem_line and problem_line.count('"') >= 4:
                    # 找到https://的位置
                    https_pos = problem_line.find('"https://')
                    if https_pos > 0:
                        # 分割字符串
                        before_https = problem_line[:https_pos]
                        after_https = problem_line[https_pos:]
                        
                        # 确保before_https以逗号结尾
                        if not before_https.rstrip().endswith(','):
                            before_https = before_https.rstrip() + ','
                        
                        # 获取缩进
                        indent_match = re.match(r'^(\s*)', problem_line)
                        indent = indent_match.group(1) if indent_match else '        '
                        
                        # 创建新的image_url行
                        image_url_line = f'{indent}"image_url": {after_https}'
                        
                        print(f"分割修复:")
                        print(f"  第一部分: {repr(before_https)}")
                        print(f"  第二部分: {repr(image_url_line)}")
                        
                        # 替换和插入
                        lines[problem_line_num] = before_https
                        lines.insert(problem_line_num + 1, image_url_line)
                        
                        # 重新组合并测试
                        fixed_content = '\n'.join(lines)
                        
                        try:
                            json_data = json.loads(fixed_content)
                            print("分割修复成功！")
                            
                            # 保存修复后的文件
                            with open(users_file, 'w', encoding='utf-8') as f:
                                json.dump(json_data, f, ensure_ascii=False, indent=2)
                            
                            print("文件修复并保存成功！")
                            return True
                            
                        except json.JSONDecodeError as e:
                            print(f"分割修复后仍有JSON错误: {e}")
                            return False
                
                return False
        else:
            print("问题行号超出文件范围")
            return False
            
    except Exception as e:
        print(f"修复过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = fix_specific_json_error()
    if success:
        print("修复成功！")
    else:
        print("修复失败！")
