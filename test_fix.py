#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的系统
"""

import sys
import traceback

def test_user_manager():
    """测试用户管理器"""
    try:
        from auth import UserManager
        user_manager = UserManager()
        print(f'✓ 用户管理器初始化成功，加载了 {len(user_manager.users)} 个用户')
        
        # 测试保存功能
        result = user_manager.save_users()
        print(f'✓ 保存测试: {"成功" if result else "失败"}')
        
        # 测试获取用户
        admin_user = user_manager.get_user('admin')
        if admin_user:
            print(f'✓ 管理员用户存在: {admin_user["username"]}')
        else:
            print('✗ 未找到管理员用户')
            
        # 测试更新积分功能
        success, message = user_manager.update_user_points('admin', 1)
        print(f'✓ 积分更新测试: {"成功" if success else "失败"} - {message}')
        
        return True
        
    except Exception as e:
        print(f'✗ 用户管理器测试失败: {e}')
        traceback.print_exc()
        return False

def test_app_import():
    """测试应用程序导入"""
    try:
        import app
        print('✓ 应用程序模块导入成功')
        return True
    except Exception as e:
        print(f'✗ 应用程序模块导入失败: {e}')
        traceback.print_exc()
        return False

def test_daily_checkin():
    """测试签到功能"""
    try:
        from auth import UserManager
        user_manager = UserManager()
        
        # 模拟签到
        admin_user = user_manager.get_user('admin')
        if admin_user:
            original_points = admin_user.get('points', 0)
            success, message = user_manager.update_user_points('admin', 10)  # 签到奖励
            if success:
                print(f'✓ 签到功能测试成功: {message}')
                return True
            else:
                print(f'✗ 签到功能测试失败: {message}')
                return False
        else:
            print('✗ 找不到测试用户')
            return False
            
    except Exception as e:
        print(f'✗ 签到功能测试失败: {e}')
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试修复后的系统...")
    print("=" * 50)
    
    tests = [
        ("用户管理器", test_user_manager),
        ("应用程序导入", test_app_import),
        ("签到功能", test_daily_checkin),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"  {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！系统修复成功。")
        print("\n建议:")
        print("1. 现在可以启动应用程序")
        print("2. 测试登录功能")
        print("3. 测试签到功能")
        print("4. 监控日志确保没有编码错误")
    else:
        print("✗ 部分测试失败，需要进一步检查。")
